'use client'

import { ReactNode, Suspense, useEffect, useState } from 'react'
import { ThemeProvider } from '@/components/theme-provider'
import { ThemeInitializer } from '@/components/theme/theme-initializer'
import { Toaster } from '@/components/ui/toaster'
import { ProviderErrorBoundary } from '@/components/error-boundaries/provider-error-boundary'
import { SettingsProvider } from '@/components/settings/settings-provider'

interface ClientWrapperProps {
  children: ReactNode
  enableAuth?: boolean
  enableSettings?: boolean
  enableKeyboard?: boolean
}

export function ClientWrapper({
  children,
  enableAuth: _enableAuth = false,
  enableSettings: _enableSettings = false,
  enableKeyboard: _enableKeyboard = false
}: ClientWrapperProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen">
        {children}
      </div>
    )
  }

  return (
    <ProviderErrorBoundary
      providerName="Client Wrapper"
      fallback={
        <div className="min-h-screen bg-background text-foreground">
          {children}
        </div>
      }
    >
      <Suspense fallback={
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="animate-pulse">Initializing application...</div>
        </div>
      }>
        <ThemeProvider>
          <ThemeInitializer />
          <SettingsProvider>
            {children}
            <Toaster />
          </SettingsProvider>
        </ThemeProvider>
      </Suspense>
    </ProviderErrorBoundary>
  )
}

// Specialized wrappers for different use cases
export function MarketingClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper>
      {children}
    </ClientWrapper>
  )
}

export function DashboardClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper
      enableAuth={true}
      enableSettings={true}
      enableKeyboard={true}
    >
      {children}
    </ClientWrapper>
  )
}

export function AuthClientWrapper({ children }: { children: ReactNode }) {
  return (
    <ClientWrapper
      enableAuth={true}
    >
      {children}
    </ClientWrapper>
  )
}
