'use client'

import { useTheme } from 'next-themes'
import { But<PERSON> } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from '@/components/ui/dropdown-menu'
import { Moon, Sun, Monitor, Feather as _Feather, Palette } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useSimpleTheme } from '@/hooks/use-simple-theme'
import Link from 'next/link'

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const { currentTheme, switchTheme, getThemesByMode, mounted: themesMounted } = useSimpleTheme()
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted || !themesMounted) {
    return (
      <Button variant="ghost" size="icon" className="h-9 w-9" disabled>
        <Palette className="h-4 w-4" />
      </Button>
    )
  }

  const lightThemes = getThemesByMode('light')
  const darkThemes = getThemesByMode('dark')

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-9 w-9"
          aria-label="Toggle theme"
        >
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="p-0">
          <Link 
            href="/customization" 
            className="flex items-center px-2 py-1.5 text-sm font-semibold hover:text-primary transition-colors w-full"
          >
            <Palette className="mr-2 h-4 w-4" />
            Customization
          </Link>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Light Themes
        </DropdownMenuLabel>
        {lightThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => switchTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Sun className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {currentTheme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Dark Themes
        </DropdownMenuLabel>
        {darkThemes.map((themeData) => (
          <DropdownMenuItem
            key={themeData.id}
            onClick={() => switchTheme(themeData.id)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <Moon className="mr-2 h-4 w-4" />
              <span>{themeData.name}</span>
            </div>
            {currentTheme === themeData.id && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => setTheme('system')}>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <Monitor className="mr-2 h-4 w-4" />
              <span>System</span>
            </div>
            {theme === 'system' && (
              <div className="w-2 h-2 rounded-full bg-primary" />
            )}
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Compact theme toggle for mobile
export function CompactThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else if (theme === 'dark') {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-8 w-8"
      aria-label={`Current theme: ${theme}. Click to change.`}
    >
      {theme === 'light' && <Sun className="h-4 w-4" />}
      {theme === 'dark' && <Moon className="h-4 w-4" />}
      {theme === 'system' && <Monitor className="h-4 w-4" />}
    </Button>
  )
}