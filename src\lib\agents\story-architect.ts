import { BaseAgent } from './base-agent'
import { BookContext } from './types'
import { 
  StoryArchitectInput, 
  StoryStructure, 
  AgentResponse 
} from '../types/agents'
import { getAIConfig } from '../config/ai-settings'
import { storyStructureSchema } from '../schemas/agent-schemas'

export class StoryArchitectAgent extends BaseAgent {
  constructor(context: BookContext) {
    super(context);
  }

  private getConfig() {
    return getAIConfig('STORY_STRUCTURE');
  }

  async generateStoryStructure(input: StoryArchitectInput): Promise<AgentResponse<StoryStructure>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)
      const config = this.getConfig();

      // Use structured completion from base class
      const result = await this.createStructuredCompletion(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        storyStructureSchema,
        'story_structure',
        config.model,
        config.temperature,
        config.max_tokens
      );

      if (!result.success) {
        // If refusal occurred, include it in the error message
        const errorMessage = result.refusal 
          ? `Model refused to generate content: ${result.refusal}`
          : result.error || 'Failed to generate story structure';
        
        return {
          success: false,
          error: errorMessage,
          executionTime: Date.now() - startTime
        };
      }

      return {
        success: true,
        data: result.data!,
        executionTime: Date.now() - startTime,
        // Note: token usage would need to be tracked separately
        // as structured outputs don't return usage in the same way
      }
    } catch (error) {
      console.error('Story structure generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: StoryArchitectInput): string {
    return `You are an expert Story Architect AI agent specializing in creating comprehensive story structures for novels. Your task is to analyze the provided project selections and story concept to generate a detailed, coherent story structure.

GUIDELINES:
- Create a structure that aligns with the selected genre (${input.projectSelections.primaryGenre})
- Follow the chosen narrative structure (${input.projectSelections.structureType})
- Match the selected tone and style (${input.projectSelections.toneOptions?.join(', ')})
- Ensure the story supports the target word count (${input.targetWordCount} words)
- Plan for ${input.targetChapters} chapters
- Include compelling conflicts and character arcs
- Maintain genre conventions while adding unique elements

STRUCTURE REQUIREMENTS:
- Create 3-5 acts based on the selected structure type
- Define major conflicts (internal, external, relational)
- Establish clear themes that align with project selections
- Plan key story events and their placement
- Consider pacing based on preferences (${input.projectSelections.pacingPreference})

RESPONSE FORMAT:
Return a valid JSON object with the StoryStructure interface, including:
- title: Creative title based on the concept
- genre: Primary genre
- themes: Array of 2-4 major themes
- acts: Array of act objects with number, title, description, keyEvents, and wordCount (not estimated wordCount)
- conflicts: Array of conflict objects with type and description
- timeline: Array of major events with optional chapter placement

Ensure all content is original, engaging, and appropriate for the target audience (${input.projectSelections.targetAudience}).`
  }

  private buildUserPrompt(input: StoryArchitectInput): string {
    return `Please create a comprehensive story structure for this novel project:

STORY CONCEPT:
${input.storyPrompt}

PROJECT DETAILS:
- Genre: ${input.projectSelections.primaryGenre}${input.projectSelections.subgenre ? ` (${input.projectSelections.subgenre})` : ''}
- Target Word Count: ${input.targetWordCount.toLocaleString()} words
- Target Chapters: ${input.targetChapters}
- Structure Type: ${input.projectSelections.structureType}
- Narrative Voice: ${input.projectSelections.narrativeVoice}
- Tense: ${input.projectSelections.tense}
- Tone: ${input.projectSelections.toneOptions?.join(', ') || 'Not specified'}
- Target Audience: ${input.projectSelections.targetAudience}
- Pacing: ${input.projectSelections.pacingPreference}

ADDITIONAL CONTEXT:
${input.projectSelections.customGenre ? `Custom Genre Elements: ${input.projectSelections.customGenre}` : ''}
${input.projectSelections.customStyleDescription ? `Style Notes: ${input.projectSelections.customStyleDescription}` : ''}
${input.projectSelections.customStructureNotes ? `Structure Notes: ${input.projectSelections.customStructureNotes}` : ''}

Generate a story structure that transforms this concept into a compelling, well-structured novel that meets all the specified requirements.`
  }
}