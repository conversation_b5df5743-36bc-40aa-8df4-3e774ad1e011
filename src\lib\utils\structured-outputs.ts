/**
 * Structured Outputs Utilities
 * Provides helper functions for using OpenAI's structured outputs feature
 * with JSON schema validation
 */

import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'

/**
 * Convert a Zod schema to OpenAI-compatible JSON schema
 * This handles the conversion and adds required metadata
 */
export function createStructuredOutputSchema<T extends z.ZodType>(
  schema: T,
  name: string,
  description?: string
) {
  const jsonSchema = zodToJsonSchema(schema, {
    name,
    nameStrategy: 'title',
    errorMessages: true,
  })

  // OpenAI requires specific format for structured outputs
  return {
    type: 'json_schema' as const,
    json_schema: {
      name,
      description,
      schema: jsonSchema,
      strict: true, // Enable strict mode for guaranteed schema compliance
    }
  }
}

/**
 * Parse structured output response with proper error handling
 * Handles both successful responses and refusals
 */
export function parseStructuredOutput<T>(
  content: string | null,
  refusal: string | null | undefined,
  schema: z.ZodType<T>
): { success: true; data: T } | { success: false; error: string; refusal?: string } {
  // Check for refusal first
  if (refusal) {
    return {
      success: false,
      error: 'Model refused to generate content',
      refusal
    }
  }

  if (!content) {
    return {
      success: false,
      error: 'No content received from model'
    }
  }

  try {
    // Parse JSON first
    const parsed = JSON.parse(content)
    
    // Validate against schema
    const result = schema.safeParse(parsed)
    
    if (result.success) {
      return {
        success: true,
        data: result.data
      }
    } else {
      return {
        success: false,
        error: `Schema validation failed: ${result.error.message}`
      }
    }
  } catch (error) {
    return {
      success: false,
      error: `Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

/**
 * Helper to check if a model supports structured outputs
 * Based on OpenAI documentation
 */
export function supportsStructuredOutputs(model: string): boolean {
  const supportedModels = [
    'gpt-4o-mini',
    'gpt-4o-2024-08-06',
    'gpt-4o',  // Latest GPT-4o models
    'gpt-4-turbo-2024-04-09',
    'gpt-4-turbo',
    'gpt-4-0125-preview',
    'gpt-4-1106-preview',
    // Note: gpt-4.1 and o4-mini are not yet confirmed to support structured outputs
    // Add fine-tuned models based on these as they're created
  ]
  
  // Check if it's a fine-tuned model based on supported models
  const isFinetuned = model.includes(':ft-')
  if (isFinetuned) {
    return supportedModels.some(base => model.includes(base))
  }
  
  return supportedModels.includes(model)
}

/**
 * Create a fallback for models that don't support structured outputs
 * Uses the legacy json_object format with manual parsing
 */
export function createJsonObjectFallback() {
  return { type: 'json_object' as const }
}