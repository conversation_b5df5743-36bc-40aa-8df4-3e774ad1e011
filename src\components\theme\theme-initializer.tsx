'use client'

import { useEffect } from 'react'
import { useTheme } from 'next-themes'
import { getThemeById, applyThemeToDocument, DEFAULT_LIGHT_THEME } from '@/lib/themes/theme-registry'

/**
 * Theme Initializer Component
 * Ensures themes are properly applied when the theme changes
 */
export function ThemeInitializer() {
  const { theme, resolvedTheme } = useTheme()

  useEffect(() => {
    // Apply theme when it changes
    const currentTheme = theme || resolvedTheme || DEFAULT_LIGHT_THEME
    const themeDefinition = getThemeById(currentTheme)
    
    if (themeDefinition) {
      applyThemeToDocument(themeDefinition)
    } else {
      // Fallback to default theme
      const defaultTheme = getThemeById(DEFAULT_LIGHT_THEME)
      if (defaultTheme) {
        applyThemeToDocument(defaultTheme)
      }
    }
  }, [theme, resolvedTheme])

  // This component doesn't render anything
  return null
}
