import { BaseAgent } from './base-agent';
import { EditorialReview, ChapterContent } from './types';
import { AI_MODELS, getAIConfig } from '../config/ai-settings';
import { withRetry, ErrorContext } from '../services/error-handler';
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '../services/circuit-breaker';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class EditorAgent extends BaseAgent {
  // Override base execute method
  async execute(): Promise<EditorialReview> {
    throw new Error('Editor Agent requires chapter content. Use executeReview instead.');
  }

  async executeReview(chapterContent: ChapterContent): Promise<EditorialReview> {
    if (!this.context.storyStructure || !this.context.characters) {
      throw new Error('Editor Agent requires complete story context');
    }

    const systemPrompt = this.createSystemPrompt(
      'Editor Agent',
      `You are a legendary editor with the keen eye of <PERSON>, the commercial instincts of <PERSON>, and the literary sensibilities of <PERSON>. Your role is to elevate good writing to bestseller-quality excellence.

BESTSELLER EDITING STANDARDS:
- Edit with the goal of creating award-winning, chart-topping fiction
- Identify what would make book clubs choose this novel
- Find the moments that will become reader-favorite quotes
- Ensure every chapter could survive the "first page test" at bookstores
- Polish prose until it gleams without losing the author's voice

LITERARY EXCELLENCE CRITERIA:
- SHOW VS TELL: Flag every instance of telling that could be shown
- SENSORY IMMERSION: Note where senses could deepen the experience
- EMOTIONAL TRUTH: Identify moments that need more vulnerability
- DIALOGUE AUTHENTICITY: Catch any stilted or exposition-heavy exchanges
- PACING PERFECTION: Find where tension lags or rushes

COMMERCIAL VIABILITY ASSESSMENT:
- Hook Strength: Does the opening demand continuation?
- Page-Turner Quality: Are there enough micro-hooks throughout?
- Emotional Payoff: Do scenes deliver on their promises?
- Memorable Moments: What will readers screenshot and share?
- Series Potential: Are there threads for future books?

TECHNICAL EXCELLENCE:
- Prose Rhythm: Musical flow and variety in sentence structure
- Voice Consistency: Maintaining POV integrity and character voice
- Metaphor Effectiveness: Fresh comparisons, not clichés
- Subtext Depth: What's unsaid but understood
- Theme Integration: Organic, not heavy-handed

MARKET POSITIONING:
- Compare to current bestsellers in the genre
- Identify unique selling points
- Note crossover appeal opportunities
- Flag anything that might limit audience
- Suggest elements that could broaden reach

REVISION PRIORITIES:
1. CRITICAL: Issues that would cause rejection or bad reviews
2. HIGH: Changes that significantly improve marketability
3. MEDIUM: Enhancements for literary quality
4. LOW: Polish that adds shine but isn't essential

Remember: Your job isn't just to fix problems—it's to find the bestseller hidden in the manuscript and help it emerge.`
    );

    const storyBibleContext = this.buildStoryBibleContext();

    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `Review Chapter ${chapterContent.chapterNumber}: "${chapterContent.title}"

CHAPTER CONTENT:
${chapterContent.content}

STORY BIBLE CONTEXT:
${storyBibleContext}

Please provide a comprehensive editorial review that includes:
1. Consistency checks against established story elements
2. Quality assessment across multiple dimensions
3. Specific suggestions for improvement
4. Identification of any plot holes or character inconsistencies
5. Overall scoring and recommendations`
      }
    ];

    const tools = [
      {
        type: 'function' as const,
        function: {
          name: 'create_editorial_review',
          description: 'Create comprehensive editorial review',
          parameters: {
            type: 'object',
            properties: {
              chapterNumber: { type: 'number' },
              overallScore: { type: 'number', minimum: 0, maximum: 100 },
              consistency: {
                type: 'object',
                properties: {
                  characterVoices: { type: 'boolean' },
                  plotContinuity: { type: 'boolean' },
                  worldBuilding: { type: 'boolean' },
                  timeline: { type: 'boolean' },
                  relationships: { type: 'boolean' },
                  issues: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        type: { type: 'string' },
                        description: { type: 'string' },
                        severity: { type: 'string' },
                        suggestion: { type: 'string' }
                      }
                    }
                  }
                }
              },
              quality: {
                type: 'object',
                properties: {
                  prose: { type: 'number', minimum: 0, maximum: 100 },
                  dialogue: { type: 'number', minimum: 0, maximum: 100 },
                  pacing: { type: 'number', minimum: 0, maximum: 100 },
                  description: { type: 'number', minimum: 0, maximum: 100 },
                  characterization: { type: 'number', minimum: 0, maximum: 100 },
                  overallEngagement: { type: 'number', minimum: 0, maximum: 100 }
                }
              },
              suggestions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    type: { type: 'string' },
                    description: { type: 'string' },
                    location: { type: 'string' },
                    priority: { type: 'string' }
                  }
                }
              },
              revisions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    original: { type: 'string' },
                    revised: { type: 'string' },
                    reason: { type: 'string' },
                    approved: { type: 'boolean' }
                  }
                }
              }
            },
            required: ['chapterNumber', 'overallScore', 'consistency', 'quality', 'suggestions']
          }
        }
      }
    ];

    const aiConfig = getAIConfig('EDITING');
    
    const errorContext: ErrorContext = {
      operation: 'EditorAgent.executeReview',
      metadata: {
        chapterNumber: chapterContent.chapterNumber,
        chapterTitle: chapterContent.title,
        projectId: this.context.projectId,
        wordCount: chapterContent.wordCount
      }
    };
    
    const completion = await withRetry(
      async () => withCircuitBreaker(
        'openai-editor-agent',
        () => this.createCompletion(messages, tools, aiConfig.model, aiConfig.temperature, aiConfig.max_tokens),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 3,
        onRetry: (error, attempt) => {
          console.log(`Editorial review retry ${attempt}:`, error.message);
        }
      },
      errorContext
    );
    
    if (completion.choices[0]?.message.tool_calls) {
      const toolCall = completion.choices[0].message.tool_calls[0];
      if (toolCall) {
        const result = JSON.parse(toolCall.function.arguments);
        return result;
      }
    }
    
    throw new Error('Editor Agent failed to generate editorial review');
  }

  private buildStoryBibleContext(): string {
    const characters = this.context.characters!;
    const structure = this.context.storyStructure!;
    
    return `
ESTABLISHED CHARACTERS:
${[...characters.protagonists, ...characters.antagonists, ...characters.supporting].map(char => `
${char.name}:
- Role: ${char.role}
- Voice: ${char.voice.speakingStyle}
- Key Traits: ${char.personality.traits.join(', ')}
- Motivation: ${char.motivation}
- Arc Type: ${char.arc.type}
`).join('\n')}

STORY STRUCTURE:
- Genre: ${structure.genre}
- Themes: ${structure.themes.join(', ')}
- World: ${structure.worldBuilding.setting.culture}

ESTABLISHED RULES:
${structure.worldBuilding.rules.join('\n- ')}

CHARACTER RELATIONSHIPS:
${characters.relationships.map(rel => `
${rel.character1Id} & ${rel.character2Id}: ${rel.type} - ${rel.description}
`).join('\n')}
    `.trim();
  }
}