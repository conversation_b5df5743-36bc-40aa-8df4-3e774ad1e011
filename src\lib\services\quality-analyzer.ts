import OpenAI from 'openai';
import { BaseService } from './base-service';
import { ServiceResponse } from './types';
import { config } from '@/lib/config';
import { AI_MODELS, AI_QUALITY_THRESHOLDS, getAIConfig } from '../config/ai-settings';

interface QualityMetrics {
  overall: number;
  coherence: number;
  style: number;
  grammar: number;
  creativity: number;
  pacing: number;
  characterConsistency: number;
  plotConsistency: number;
  emotionalImpact: number;
  readability: number;
}

interface QualityAnalysisResult {
  metrics: QualityMetrics;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  embedding?: number[];
}

interface ContentComparison {
  similarity: number;
  consistencyScore: number;
  styleDifference: number;
  issues: string[];
}

export class QualityAnalyzer extends BaseService {
  private openai: OpenAI;
  private embeddings: Map<string, number[]> = new Map();
  private referenceEmbeddings: Map<string, number[]> = new Map();

  constructor() {
    super({
      name: 'quality-analyzer',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/quality/analyze', '/api/quality/compare'],
      dependencies: [],
      healthCheck: '/api/quality/health'
    });

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string }>> {
    return this.createResponse(true, {
      status: `${this.embeddings.size} embeddings cached`
    });
  }

  async shutdown(): Promise<void> {
    this.embeddings.clear();
    this.referenceEmbeddings.clear();
    this.setStatus('inactive');
  }

  /**
   * Analyze content quality using AI and embeddings
   */
  async analyzeContentQuality(
    content: string,
    contentType: 'chapter' | 'dialogue' | 'description' | 'scene' | 'character',
    context?: {
      previousContent?: string;
      characterProfiles?: Record<string, unknown>;
      storyContext?: Record<string, unknown>;
    }
  ): Promise<ServiceResponse<QualityAnalysisResult>> {
    return this.withErrorHandling(async () => {
      // Generate embedding for the content
      const embedding = await this.generateEmbedding(content);
      
      // Use AI to analyze quality with structured output
      const aiConfig = getAIConfig('ANALYSIS');
      const analysisPrompt = this.buildQualityAnalysisPrompt(content, contentType, context);
      
      const errorContext: ErrorContext = {
        operation: 'QualityAnalyzer.analyzeContentQuality',
        metadata: {
          contentType,
          contentLength: content.length,
          model: aiConfig.model
        }
      };
      
      const completion = await withRetry(
        async () => withCircuitBreaker(
          'openai-quality-analyzer',
          () => this.openai.chat.completions.create({
            model: aiConfig.model,
            messages: [
              {
                role: 'system',
                content: `You are an expert literary critic and editor. Analyze the provided ${contentType} content and provide detailed quality metrics.`
              },
              { role: 'user', content: analysisPrompt }
            ],
            temperature: aiConfig.temperature,
            max_tokens: aiConfig.max_tokens,
            tools: [
              {
                type: 'function',
                function: {
                  name: 'analyze_quality',
                  description: 'Provide detailed quality analysis',
                  parameters: {
                    type: 'object',
                    properties: {
                      metrics: {
                        type: 'object',
                        properties: {
                          overall: { type: 'number', minimum: 0, maximum: 100 },
                          coherence: { type: 'number', minimum: 0, maximum: 100 },
                          style: { type: 'number', minimum: 0, maximum: 100 },
                          grammar: { type: 'number', minimum: 0, maximum: 100 },
                          creativity: { type: 'number', minimum: 0, maximum: 100 },
                          pacing: { type: 'number', minimum: 0, maximum: 100 },
                          characterConsistency: { type: 'number', minimum: 0, maximum: 100 },
                          plotConsistency: { type: 'number', minimum: 0, maximum: 100 },
                          emotionalImpact: { type: 'number', minimum: 0, maximum: 100 },
                          readability: { type: 'number', minimum: 0, maximum: 100 }
                        },
                        required: ['overall', 'coherence', 'style', 'grammar', 'creativity', 'pacing', 'characterConsistency', 'plotConsistency', 'emotionalImpact', 'readability']
                      },
                      strengths: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                      weaknesses: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                      suggestions: {
                        type: 'array',
                        items: { type: 'string' }
                      }
                    },
                    required: ['metrics', 'strengths', 'weaknesses', 'suggestions']
                  }
                }
              }
            ],
            tool_choice: { type: 'function', function: { name: 'analyze_quality' } }
          }),
          CIRCUIT_BREAKER_PRESETS.OPENAI
        ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`Quality analysis retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      let result: QualityAnalysisResult = {
        metrics: {
          overall: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          coherence: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          style: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          grammar: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          creativity: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          pacing: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          characterConsistency: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          plotConsistency: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          emotionalImpact: AI_QUALITY_THRESHOLDS.ACCEPTABLE,
          readability: AI_QUALITY_THRESHOLDS.ACCEPTABLE
        },
        strengths: [],
        weaknesses: [],
        suggestions: [],
        embedding
      };

      if (completion.choices[0]?.message.tool_calls?.[0]) {
        const toolCall = completion.choices[0].message.tool_calls[0];
        if (toolCall.function.arguments) {
          const analysis = JSON.parse(toolCall.function.arguments);
          result = { ...analysis, embedding };
        }
      }

      // Ensure minimum quality thresholds
      const minScore = AI_QUALITY_THRESHOLDS.MINIMUM_SCORES[
        contentType.toUpperCase() as keyof typeof AI_QUALITY_THRESHOLDS.MINIMUM_SCORES
      ] || AI_QUALITY_THRESHOLDS.ACCEPTABLE;

      if (result.metrics.overall < minScore) {
        result.weaknesses.push(`Overall quality below minimum threshold of ${minScore}`);
        result.suggestions.push(`Revise to improve overall quality to at least ${minScore}`);
      }

      return result;
    });
  }

  /**
   * Compare two pieces of content for consistency and style
   */
  async compareContent(
    content1: string,
    content2: string,
    comparisonType: 'style' | 'consistency' | 'both' = 'both'
  ): Promise<ServiceResponse<ContentComparison>> {
    return this.withErrorHandling(async () => {
      // Generate embeddings for both contents
      const [embedding1, embedding2] = await Promise.all([
        this.generateEmbedding(content1),
        this.generateEmbedding(content2)
      ]);

      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(embedding1, embedding2);

      // Use AI for detailed comparison
      const aiConfig = getAIConfig('ANALYSIS');
      
      const errorContext: ErrorContext = {
        operation: 'QualityAnalyzer.compareContent',
        metadata: {
          comparisonType,
          content1Length: content1.length,
          content2Length: content2.length,
          model: aiConfig.model
        }
      };
      
      const comparison = await withRetry(
        async () => withCircuitBreaker(
          'openai-quality-analyzer',
          () => this.openai.chat.completions.create({
            model: aiConfig.model,
            messages: [
              {
                role: 'system',
                content: 'You are an expert at comparing literary content for consistency and style differences.'
              },
              {
                role: 'user',
                content: `Compare these two pieces of content for ${comparisonType}:

Content 1:
${content1.substring(0, 1000)}${content1.length > 1000 ? '...' : ''}

Content 2:
${content2.substring(0, 1000)}${content2.length > 1000 ? '...' : ''}

Provide a detailed analysis of:
1. Consistency issues (plot, characters, world-building)
2. Style differences (voice, tone, pacing)
3. Specific issues that need attention`
          }
        ],
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.max_tokens,
        tools: [
          {
            type: 'function',
            function: {
              name: 'compare_content',
              description: 'Compare content for consistency and style',
              parameters: {
                type: 'object',
                properties: {
                  consistencyScore: { type: 'number', minimum: 0, maximum: 100 },
                  styleDifference: { type: 'number', minimum: 0, maximum: 100 },
                  issues: {
                    type: 'array',
                    items: { type: 'string' }
                  }
                },
                required: ['consistencyScore', 'styleDifference', 'issues']
              }
            }
          }
        ],
        tool_choice: { type: 'function', function: { name: 'compare_content' } }
          }),
          CIRCUIT_BREAKER_PRESETS.OPENAI
        ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`Content comparison retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      let result: ContentComparison = {
        similarity: similarity * 100,
        consistencyScore: 80,
        styleDifference: 20,
        issues: []
      };

      if (comparison.choices[0]?.message.tool_calls?.[0]) {
        const toolCall = comparison.choices[0].message.tool_calls[0];
        if (toolCall.function.arguments) {
          const analysis = JSON.parse(toolCall.function.arguments);
          result = { ...result, ...analysis };
        }
      }

      return result;
    });
  }

  /**
   * Generate embeddings for content
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    const cacheKey = this.hashText(text);
    
    if (this.embeddings.has(cacheKey)) {
      return this.embeddings.get(cacheKey)!;
    }

    const errorContext: ErrorContext = {
      operation: 'QualityAnalyzer.generateEmbedding',
      metadata: {
        textLength: text.length
      }
    };
    
    const response = await withRetry(
      async () => withCircuitBreaker(
        'openai-embeddings',
        () => this.openai.embeddings.create({
          model: AI_MODELS.EMBEDDING,
          input: text.substring(0, 8000), // Limit to max input size
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 2,
        onRetry: (error, attempt) => {
          console.log(`Embedding generation retry ${attempt}:`, error.message);
        }
      },
      errorContext
    );

    const embedding = response.data[0].embedding;
    this.embeddings.set(cacheKey, embedding);
    
    return embedding;
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Embeddings must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    return normA === 0 || normB === 0 ? 0 : dotProduct / (normA * normB);
  }

  /**
   * Build quality analysis prompt
   */
  private buildQualityAnalysisPrompt(
    content: string,
    contentType: string,
    context?: {
      previousContent?: string;
      characterProfiles?: Record<string, unknown>;
      storyContext?: Record<string, unknown>;
    }
  ): string {
    let prompt = `Analyze this ${contentType} content for quality:

${content}

Consider the following aspects:
1. Overall quality and professionalism
2. Coherence and logical flow
3. Writing style and voice consistency
4. Grammar and technical correctness
5. Creativity and originality
6. Pacing and engagement
7. Character consistency (if applicable)
8. Plot consistency (if applicable)
9. Emotional impact and resonance
10. Readability and clarity`;

    if (context?.previousContent) {
      prompt += `\n\nPrevious content for context:\n${context.previousContent.substring(0, 1000)}...`;
    }

    if (context?.characterProfiles) {
      prompt += `\n\nCharacter profiles:\n${JSON.stringify(context.characterProfiles, null, 2).substring(0, 1000)}...`;
    }

    return prompt;
  }

  /**
   * Simple hash function for text caching
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Store reference embeddings for style matching
   */
  async storeReferenceEmbedding(
    name: string,
    content: string
  ): Promise<ServiceResponse<{ stored: boolean }>> {
    return this.withErrorHandling(async () => {
      const embedding = await this.generateEmbedding(content);
      this.referenceEmbeddings.set(name, embedding);
      return { stored: true };
    });
  }

  /**
   * Compare content against reference style
   */
  async compareToReference(
    content: string,
    referenceName: string
  ): Promise<ServiceResponse<number>> {
    return this.withErrorHandling(async () => {
      const referenceEmbedding = this.referenceEmbeddings.get(referenceName);
      if (!referenceEmbedding) {
        throw new Error(`Reference embedding '${referenceName}' not found`);
      }

      const contentEmbedding = await this.generateEmbedding(content);
      return this.cosineSimilarity(contentEmbedding, referenceEmbedding) * 100;
    });
  }
}

// Singleton instance
export const qualityAnalyzer = new QualityAnalyzer();