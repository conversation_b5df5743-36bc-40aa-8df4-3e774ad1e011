"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { getAllThemes, DEFAULT_LIGHT_THEME, DEFAULT_DARK_THEME, applyThemeToDocument, getThemeById } from "@/lib/themes/theme-registry"

type ThemeProviderProps = {
  children: React.ReactNode
  attribute?: 'class' | 'data-theme' | 'data-mode'
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
  storageKey?: string
  themes?: string[]
  value?: { [key: string]: string }
  nonce?: string
  enableColorScheme?: boolean
}

export function ThemeProvider({
  children,
  attribute = "class",
  defaultTheme = DEFAULT_LIGHT_THEME,
  enableSystem = true,
  disableTransitionOnChange = false,
  storageKey = "bookscribe-theme",
  ...props
}: ThemeProviderProps) {
  const allThemes = getAllThemes()
  const themeValues = allThemes.map(theme => theme.id)

  // Initialize theme on mount
  React.useEffect(() => {
    // Apply the default theme immediately to prevent flash
    const savedTheme = localStorage.getItem(storageKey)
    const themeToApply = savedTheme && getThemeById(savedTheme) ? savedTheme : defaultTheme
    const theme = getThemeById(themeToApply)

    if (theme) {
      applyThemeToDocument(theme)
    }
  }, [defaultTheme, storageKey])

  return (
    <NextThemesProvider
      attribute={attribute}
      defaultTheme={defaultTheme}
      enableSystem={enableSystem}
      disableTransitionOnChange={disableTransitionOnChange}
      storageKey={storageKey}
      themes={[...themeValues, 'system', 'light', 'dark']}
      value={{
        light: DEFAULT_LIGHT_THEME,
        dark: DEFAULT_DARK_THEME,
        system: 'system',
        ...themeValues.reduce((acc, themeId) => {
          acc[themeId] = themeId
          return acc
        }, {} as Record<string, string>)
      }}
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}